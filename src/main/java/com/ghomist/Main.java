package com.ghomist;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import java.util.Arrays;

import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.ElasticsearchTransportBase;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;

public class Main {
  public static void main(String[] args) {
    String str = "";
    String dateFrom = "";
    String dateTo = "";
    long[] longList = new long[] {1, 2, 3, 4, 5};

    var list =
        Arrays.stream(longList)
            .mapToObj(l -> new FieldValue.Builder().longValue(l).build())
            .toList();

    var builder =
        new BoolQuery.Builder()
            // termQuery
            .filter(QueryBuilders.term().field("abc").value(str).build())
            .filter(f -> f.term(q -> q.field("abc").value(str)))

            // rangeQuery
            .filter(
                QueryBuilders.range().date(d -> d.field("abc").gte(dateFrom).lte(dateTo)).build())
            .filter(f -> f.range(b -> b.date(d -> d.field("abc").gte(dateFrom).lte(dateTo))))

            // termsQuery
            .filter(QueryBuilders.terms(a -> a.field("abc").terms(x -> x.value(list))).terms())
            .filter(f -> f.terms(b -> b.field("abc").terms(t -> t.value(list))))

            // mustNot
            .mustNot(QueryBuilders.term().field("abc").value(str).build())
            .mustNot(f -> f.term(q -> q.field("abc").value(str)));

    var ret = new NativeQueryBuilder().withFilter(builder.build()._toQuery());

    var req =
        SearchRequest.of(
            b ->
                b.trackTotalHits(t -> t.enabled(true))
                    .sort(s -> s.field(f -> f.field("abc").order(SortOrder.Desc)))
                    .from(0)
                    .size(10));

    SearchRequest searchRequest = new SearchRequest(INVOKE_LOG_PREFIX + "*").source(query);

    ElasticsearchTransport transport = null;
    try (var client = new ElasticsearchClient(transport)) {
      client.search(req, Object.class);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}
